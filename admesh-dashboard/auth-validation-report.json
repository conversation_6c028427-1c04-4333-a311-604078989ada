{"timestamp": "2025-07-02T03:46:05.576Z", "summary": {"passed": 3, "failed": 4, "warnings": 1, "total": 8}, "details": [{"name": "Enhanced API Client", "status": "PASSED", "critical": true}, {"name": "Advanced Token Manager", "status": "FAILED", "critical": true, "error": "Missing: event listeners"}, {"name": "<PERSON><PERSON><PERSON>", "status": "PASSED", "critical": true}, {"name": "Secure Storage", "status": "FAILED", "critical": true, "error": "Missing: session validation, activity tracking"}, {"name": "Enhanced useAuth Hook", "status": "PASSED", "critical": true}, {"name": "TypeScript Compilation", "status": "FAILED", "critical": true, "error": "Command failed: npx tsc --noEmit --project tsconfig.json"}, {"name": "ESLint Validation", "status": "WARNING", "critical": false, "error": "Command failed: npx eslint src/lib/auth-*.ts src/lib/api-helpers.ts src/lib/secure-storage.ts --quiet\n(node:17882) ESLintIgnoreWarning: The \".eslintignore\" file is no longer supported. Switch to using the \"ignores\" property in \"eslint.config.js\": https://eslint.org/docs/latest/use/configure/migration-guide#ignoring-files\n(Use `node --trace-warnings ...` to show where the warning was created)\n"}, {"name": "Unit Tests", "status": "FAILED", "critical": true, "error": "Command failed: npx vitest run src/lib/__tests__/auth-integration.test.ts\n\n⎯⎯⎯⎯⎯⎯ Failed Suites 1 ⎯⎯⎯⎯⎯⎯⎯\n\n FAIL  src/lib/__tests__/auth-integration.test.ts [ src/lib/__tests__/auth-integration.test.ts ]\nError: Cannot find package '@/lib/firebase' imported from '/Users/<USER>/Desktop/AdMesh/protocol/admesh-dashboard/src/lib/auth-token-manager.ts'\n ❯ src/lib/auth-token-manager.ts:5:1\n      3|  */\n      4| \n      5| import { auth } from '@/lib/firebase';\n       | ^\n      6| import { User, onAuthStateChanged } from 'firebase/auth';\n      7| import { secureStorage, tokenStorage, activityTracker } from '@/lib/se…\n\nCaused by: Error: Failed to load url @/lib/firebase (resolved id: @/lib/firebase) in /Users/<USER>/Desktop/AdMesh/protocol/admesh-dashboard/src/lib/auth-token-manager.ts. Does the file exist?\n ❯ loadAndTransform ../node_modules/vite/dist/node/chunks/dep-DBxKXgDP.js:35725:17\n\n⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[1/1]⎯\n\n"}], "recommendations": ["Monitor token refresh frequency in production", "Set up alerts for authentication failures", "Regularly audit secure storage implementation", "Test authentication flow across different devices", "Implement authentication analytics"]}