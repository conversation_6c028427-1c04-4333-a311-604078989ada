# Enhanced Firebase Authentication System

This document describes the comprehensive authentication improvements implemented for the AdMesh dashboard, including proper token management, error handling, and security features.

## Overview

The enhanced authentication system provides:

- **Automatic Token Management**: Seamless token refresh and caching
- **Enhanced Error Handling**: User-friendly error messages instead of toasts
- **Secure Storage**: Encrypted token storage with activity tracking
- **Retry Logic**: Automatic retry for network and server errors
- **Background Monitoring**: Proactive token refresh before expiration

## Architecture

### Core Components

1. **AdvancedTokenManager** (`src/lib/auth-token-manager.ts`)
   - Manages Firebase token lifecycle
   - Automatic background refresh
   - Event-driven architecture
   - Secure token caching

2. **Enhanced API Client** (`src/lib/api-helpers.ts`)
   - Centralized HTTP client with authentication
   - Automatic token injection
   - Retry logic for failures
   - Error handling and recovery

3. **Error Handler** (`src/lib/auth-error-handler.ts`)
   - Comprehensive error mapping
   - User-friendly error messages
   - Context-aware error handling
   - Retry recommendations

4. **Secure Storage** (`src/lib/secure-storage.ts`)
   - Encrypted token storage
   - Session validation
   - Activity tracking
   - Automatic cleanup

5. **Enhanced useAuth Hook** (`src/hooks/use-auth.ts`)
   - Integrated token management
   - Activity tracking
   - Enhanced user state management

## Key Features

### 1. Automatic Token Refresh

```typescript
// Token is automatically refreshed 5 minutes before expiration
const token = await tokenManager.getToken();

// Force refresh if needed
const freshToken = await tokenManager.getToken(true);
```

**Benefits:**
- Prevents authentication interruptions
- Background refresh without user interaction
- Configurable refresh timing
- Retry logic for failed refreshes

### 2. Enhanced API Client

```typescript
// Automatic authentication and retry
const response = await apiClient.get('/api/user/profile');

if (response.success) {
  console.log('User data:', response.data);
} else {
  console.error('Error:', response.error);
}
```

**Features:**
- Automatic token injection
- 401 error handling with token refresh
- Retry logic for 5xx errors and rate limits
- Comprehensive error responses

### 3. Secure Token Storage

```typescript
// Tokens are automatically encrypted and stored
tokenStorage.set(token, expiry);

// Automatic validation and cleanup
const isValid = tokenStorage.isValid();
```

**Security Features:**
- Session-based encryption key
- Automatic expiry validation
- Secure cleanup on logout
- Activity-based session management

### 4. Enhanced Error Handling

```typescript
// Context-aware error handling
const context: AuthErrorContext = {
  operation: 'signin',
  email: '<EMAIL>',
  provider: 'email',
};

const authError = handleAuthError(error, context);
const displayError = formatErrorForDisplay(authError);
```

**Improvements:**
- User-friendly error messages
- Actionable error guidance
- Context-aware responses
- Proper error categorization

## Implementation Guide

### 1. Basic Usage

```typescript
import { useAuth } from '@/hooks/use-auth';
import { apiClient } from '@/lib/api-helpers';

function MyComponent() {
  const { user, isAuthenticated, getAuthToken } = useAuth();

  const fetchData = async () => {
    const response = await apiClient.get('/api/data');
    if (response.success) {
      // Handle success
    } else {
      // Handle error
    }
  };

  return (
    <div>
      {isAuthenticated() ? (
        <button onClick={fetchData}>Fetch Data</button>
      ) : (
        <p>Please sign in</p>
      )}
    </div>
  );
}
```

### 2. Error Handling in Forms

```typescript
import { handleAuthError, formatErrorForDisplay } from '@/lib/auth-error-handler';

const handleSignIn = async (email: string, password: string) => {
  try {
    await signInWithEmailAndPassword(auth, email, password);
  } catch (err) {
    const authError = handleAuthError(err, {
      operation: 'signin',
      email,
      provider: 'email',
    });
    
    const displayError = formatErrorForDisplay(authError);
    setError(displayError.message);
  }
};
```

### 3. Manual Token Management

```typescript
import { tokenManager } from '@/lib/auth-token-manager';

// Get current token
const token = await tokenManager.getToken();

// Force refresh
const freshToken = await tokenManager.refreshToken();

// Listen for token events
tokenManager.on('tokenRefreshed', (newToken) => {
  console.log('Token refreshed:', newToken);
});

tokenManager.on('refreshFailed', (error) => {
  console.error('Token refresh failed:', error);
});
```

## Configuration

### Token Refresh Settings

```typescript
// Default configuration in AdvancedTokenManager
const REFRESH_BUFFER_TIME = 5 * 60 * 1000; // 5 minutes before expiry
const MIN_REFRESH_INTERVAL = 30 * 1000; // Minimum 30 seconds between refreshes
const MAX_RETRY_ATTEMPTS = 3; // Maximum retry attempts
```

### API Client Settings

```typescript
// Default configuration in ApiClient
const maxRetries = 3; // Maximum retry attempts
const retryDelay = 1000; // Base delay for exponential backoff
```

### Secure Storage Settings

```typescript
// Default configuration in SecureStorage
const SESSION_TIMEOUT = 24 * 60 * 60 * 1000; // 24 hours
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
```

## Security Considerations

### 1. Token Storage
- Tokens are stored in sessionStorage (cleared on tab close)
- Basic obfuscation applied to stored tokens
- Automatic cleanup on expiry or logout

### 2. Session Management
- Activity tracking prevents stale sessions
- Automatic session validation
- Device fingerprinting for security

### 3. Error Information
- Sensitive information filtered from error messages
- User-friendly messages without technical details
- Proper error categorization for security

## Testing

### Running Tests

```bash
# Run authentication tests
npm run test src/lib/__tests__/auth-integration.test.ts

# Run validation script
node scripts/validate-auth.js

# Type checking
npx tsc --noEmit
```

### Manual Testing Checklist

- [ ] Sign in with email/password
- [ ] Sign in with Google
- [ ] Token refresh on expiry
- [ ] Error handling for invalid credentials
- [ ] Network error recovery
- [ ] Session persistence across page reloads
- [ ] Proper cleanup on sign out
- [ ] Activity tracking functionality

## Monitoring and Analytics

### Key Metrics to Track

1. **Authentication Success Rate**
   - Sign-in success/failure ratio
   - Provider-specific success rates

2. **Token Refresh Performance**
   - Refresh frequency
   - Refresh success rate
   - Time to refresh

3. **Error Rates**
   - Authentication error frequency
   - Error type distribution
   - User retry behavior

4. **Session Management**
   - Average session duration
   - Session timeout frequency
   - Activity patterns

### Logging

```typescript
// Token manager events
tokenManager.on('tokenRefreshed', (token) => {
  analytics.track('token_refreshed', {
    timestamp: Date.now(),
    tokenLength: token.length,
  });
});

tokenManager.on('refreshFailed', (error) => {
  analytics.track('token_refresh_failed', {
    error: error.message,
    timestamp: Date.now(),
  });
});
```

## Troubleshooting

### Common Issues

1. **Token Refresh Failures**
   - Check network connectivity
   - Verify Firebase configuration
   - Check user authentication status

2. **Storage Issues**
   - Verify browser storage availability
   - Check for storage quota limits
   - Clear corrupted storage data

3. **API Client Errors**
   - Verify API endpoint URLs
   - Check CORS configuration
   - Validate request headers

### Debug Mode

```typescript
// Enable debug logging
localStorage.setItem('admesh_debug_auth', 'true');

// Check token manager status
console.log('Token info:', tokenManager.getTokenExpiry());
console.log('Session valid:', activityTracker.isValid());
```

## Migration Guide

### From Legacy Authentication

1. **Update Imports**
   ```typescript
   // Old
   import { fetchWithAuth } from '@/lib/api-helpers';
   
   // New
   import { apiClient } from '@/lib/api-helpers';
   ```

2. **Update API Calls**
   ```typescript
   // Old
   const response = await fetchWithAuth(url, options);
   
   // New
   const response = await apiClient.get(endpoint);
   ```

3. **Update Error Handling**
   ```typescript
   // Old
   toast.error("Sign-in failed");
   
   // New
   const authError = handleAuthError(error, context);
   setError(formatErrorForDisplay(authError).message);
   ```

## Future Enhancements

1. **Biometric Authentication**
   - WebAuthn integration
   - Fingerprint/Face ID support

2. **Advanced Security**
   - JWT signature validation
   - Token rotation strategies
   - Multi-factor authentication

3. **Performance Optimization**
   - Token prefetching
   - Background sync
   - Offline support

4. **Analytics Integration**
   - Authentication flow tracking
   - Performance monitoring
   - Security event logging
