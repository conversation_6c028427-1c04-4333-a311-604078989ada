/**
 * Debug utilities for authentication troubleshooting
 */

import { auth } from '@/lib/firebase';
import { tokenManager } from '@/lib/auth-token-manager';
import { apiClient } from '@/lib/api-helpers';

export interface AuthDebugInfo {
  firebaseUser: {
    exists: boolean;
    uid?: string;
    email?: string;
    emailVerified?: boolean;
  };
  token: {
    exists: boolean;
    length?: number;
    preview?: string;
    claims?: Record<string, unknown>;
  };
  apiTest: {
    success: boolean;
    status?: number;
    error?: unknown;
    data?: unknown;
  };
  timestamp: string;
}

/**
 * Comprehensive authentication debug function
 */
export async function debugAuthentication(): Promise<AuthDebugInfo> {
  const debugInfo: AuthDebugInfo = {
    firebaseUser: { exists: false },
    token: { exists: false },
    apiTest: { success: false },
    timestamp: new Date().toISOString(),
  };

  try {
    // Check Firebase user
    const currentUser = auth.currentUser;
    if (currentUser) {
      debugInfo.firebaseUser = {
        exists: true,
        uid: currentUser.uid,
        email: currentUser.email || undefined,
        emailVerified: currentUser.emailVerified,
      };

      // Check token
      try {
        const token = await tokenManager.getToken();
        if (token) {
          debugInfo.token = {
            exists: true,
            length: token.length,
            preview: token.substring(0, 50) + '...',
          };

          // Try to decode token claims
          try {
            const tokenResult = await currentUser.getIdTokenResult();
            debugInfo.token.claims = tokenResult.claims;

            console.log('🔍 Token Claims Analysis:', {
              hasRole: !!tokenResult.claims.role,
              role: tokenResult.claims.role,
              hasAdmin: !!tokenResult.claims.admin,
              admin: tokenResult.claims.admin,
              allClaims: tokenResult.claims
            });
          } catch (claimsError) {
            console.error('Error getting token claims:', claimsError);
          }

          // Test API call
          try {
            const response = await apiClient.get('/api/auth/me');
            debugInfo.apiTest = {
              success: response.success,
              status: response.error?.status,
              error: response.error,
              data: response.success ? 'Data received' : undefined,
            };
          } catch (apiError) {
            debugInfo.apiTest = {
              success: false,
              error: apiError instanceof Error ? apiError.message : apiError,
            };
          }
        } else {
          debugInfo.token = {
            exists: false,
          };
        }
      } catch (tokenError) {
        console.error('Error getting token:', tokenError);
        debugInfo.token = {
          exists: false,
        };
      }
    }
  } catch (error) {
    console.error('Error in debugAuthentication:', error);
  }

  return debugInfo;
}

/**
 * Test direct API call with manual token
 */
export async function testDirectApiCall(): Promise<{
  success: boolean;
  status?: number;
  data?: unknown;
  error?: unknown;
}> {
  try {
    const currentUser = auth.currentUser;
    if (!currentUser) {
      return { success: false, error: 'No current user' };
    }

    // Get token directly from Firebase
    const token = await currentUser.getIdToken(true); // Force refresh

    // Make direct fetch call
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/auth/me`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    const data = await response.json();

    return {
      success: response.ok,
      status: response.status,
      data: response.ok ? data : undefined,
      error: !response.ok ? data : undefined,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : error,
    };
  }
}

/**
 * Log comprehensive debug information
 */
export async function logAuthDebugInfo(): Promise<void> {
  console.group('🔍 Authentication Debug Information');
  
  const debugInfo = await debugAuthentication();
  console.log('Debug Info:', debugInfo);

  const directApiTest = await testDirectApiCall();
  console.log('Direct API Test:', directApiTest);

  // Test environment variables
  console.log('Environment:', {
    apiBaseUrl: process.env.NEXT_PUBLIC_API_BASE_URL,
    nodeEnv: process.env.NODE_ENV,
  });

  console.groupEnd();
}

/**
 * Quick authentication status check
 */
export function getAuthStatus(): {
  hasUser: boolean;
  userEmail?: string;
  isSignedIn: boolean;
} {
  const currentUser = auth.currentUser;
  return {
    hasUser: !!currentUser,
    userEmail: currentUser?.email || undefined,
    isSignedIn: !!currentUser,
  };
}

// Make debug functions available globally in development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).debugAuth = {
    debugAuthentication,
    testDirectApiCall,
    logAuthDebugInfo,
    getAuthStatus,
  };
  
  console.log('🔧 Auth debug utilities available at window.debugAuth');
}
