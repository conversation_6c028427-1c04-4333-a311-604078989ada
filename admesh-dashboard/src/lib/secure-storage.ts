/**
 * Secure storage utilities for handling sensitive data like authentication tokens
 * Implements best practices for client-side token storage
 */

// Storage keys
const STORAGE_KEYS = {
  TOKEN_CACHE: 'admesh_token_cache',
  USER_PREFERENCES: 'admesh_user_prefs',
  SESSION_DATA: 'admesh_session',
} as const;

// Token cache interface
interface TokenCacheData {
  token: string;
  expiry: number;
  issuedAt: number;
  encrypted?: boolean;
}

// Session data interface
interface SessionData {
  lastActivity: number;
  deviceId: string;
  userAgent: string;
}

/**
 * Secure storage manager with encryption and validation
 */
export class SecureStorage {
  private static instance: SecureStorage;
  private encryptionKey: string | null = null;
  private readonly SESSION_TIMEOUT = 24 * 60 * 60 * 1000; // 24 hours

  private constructor() {
    this.initializeEncryption();
  }

  static getInstance(): SecureStorage {
    if (!SecureStorage.instance) {
      SecureStorage.instance = new SecureStorage();
    }
    return SecureStorage.instance;
  }

  /**
   * Initialize encryption key for sensitive data
   */
  private initializeEncryption(): void {
    // Generate a session-specific encryption key
    // Note: This is basic obfuscation, not true encryption
    // For production, consider using Web Crypto API
    if (typeof window !== 'undefined') {
      this.encryptionKey = this.generateSessionKey();
    }
  }

  /**
   * Generate a session-specific key for basic obfuscation
   */
  private generateSessionKey(): string {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2);
    return btoa(timestamp + random).substring(0, 16);
  }

  /**
   * Basic obfuscation (not true encryption)
   */
  private obfuscate(data: string): string {
    if (!this.encryptionKey) return data;
    
    let result = '';
    for (let i = 0; i < data.length; i++) {
      const keyChar = this.encryptionKey.charCodeAt(i % this.encryptionKey.length);
      const dataChar = data.charCodeAt(i);
      result += String.fromCharCode(dataChar ^ keyChar);
    }
    return btoa(result);
  }

  /**
   * Basic deobfuscation
   */
  private deobfuscate(data: string): string {
    if (!this.encryptionKey) return data;
    
    try {
      const decoded = atob(data);
      let result = '';
      for (let i = 0; i < decoded.length; i++) {
        const keyChar = this.encryptionKey.charCodeAt(i % this.encryptionKey.length);
        const dataChar = decoded.charCodeAt(i);
        result += String.fromCharCode(dataChar ^ keyChar);
      }
      return result;
    } catch {
      return data; // Return original if deobfuscation fails
    }
  }

  /**
   * Store token cache securely
   */
  setTokenCache(tokenData: Omit<TokenCacheData, 'encrypted'>): void {
    if (typeof window === 'undefined') return;

    try {
      const dataToStore: TokenCacheData = {
        ...tokenData,
        encrypted: true,
      };

      // Obfuscate the token
      const obfuscatedData = {
        ...dataToStore,
        token: this.obfuscate(tokenData.token),
      };

      sessionStorage.setItem(STORAGE_KEYS.TOKEN_CACHE, JSON.stringify(obfuscatedData));
    } catch (error) {
      console.error('Failed to store token cache:', error);
    }
  }

  /**
   * Retrieve token cache securely
   */
  getTokenCache(): TokenCacheData | null {
    if (typeof window === 'undefined') return null;

    try {
      const stored = sessionStorage.getItem(STORAGE_KEYS.TOKEN_CACHE);
      if (!stored) return null;

      const data: TokenCacheData = JSON.parse(stored);
      
      // Check if token is expired
      if (Date.now() >= data.expiry) {
        this.clearTokenCache();
        return null;
      }

      // Deobfuscate the token if it was encrypted
      if (data.encrypted) {
        data.token = this.deobfuscate(data.token);
      }

      return data;
    } catch (error) {
      console.error('Failed to retrieve token cache:', error);
      this.clearTokenCache();
      return null;
    }
  }

  /**
   * Clear token cache
   */
  clearTokenCache(): void {
    if (typeof window === 'undefined') return;
    
    try {
      sessionStorage.removeItem(STORAGE_KEYS.TOKEN_CACHE);
    } catch (error) {
      console.error('Failed to clear token cache:', error);
    }
  }

  /**
   * Store session data
   */
  setSessionData(data: Partial<SessionData>): void {
    if (typeof window === 'undefined') return;

    try {
      const existing = this.getSessionData();
      const sessionData: SessionData = {
        lastActivity: Date.now(),
        deviceId: this.getDeviceId(),
        userAgent: navigator.userAgent,
        ...existing,
        ...data,
      };

      localStorage.setItem(STORAGE_KEYS.SESSION_DATA, JSON.stringify(sessionData));
    } catch (error) {
      console.error('Failed to store session data:', error);
    }
  }

  /**
   * Retrieve session data
   */
  getSessionData(): SessionData | null {
    if (typeof window === 'undefined') return null;

    try {
      const stored = localStorage.getItem(STORAGE_KEYS.SESSION_DATA);
      if (!stored) return null;

      const data: SessionData = JSON.parse(stored);
      
      // Check if session is expired
      if (Date.now() - data.lastActivity > this.SESSION_TIMEOUT) {
        this.clearSessionData();
        return null;
      }

      return data;
    } catch (error) {
      console.error('Failed to retrieve session data:', error);
      this.clearSessionData();
      return null;
    }
  }

  /**
   * Clear session data
   */
  clearSessionData(): void {
    if (typeof window === 'undefined') return;
    
    try {
      localStorage.removeItem(STORAGE_KEYS.SESSION_DATA);
    } catch (error) {
      console.error('Failed to clear session data:', error);
    }
  }

  /**
   * Generate or retrieve device ID
   */
  private getDeviceId(): string {
    const stored = localStorage.getItem('admesh_device_id');
    if (stored) return stored;

    const deviceId = 'device_' + Date.now() + '_' + Math.random().toString(36).substring(2);
    localStorage.setItem('admesh_device_id', deviceId);
    return deviceId;
  }

  /**
   * Clear all stored data
   */
  clearAll(): void {
    this.clearTokenCache();
    this.clearSessionData();
    
    if (typeof window !== 'undefined') {
      try {
        localStorage.removeItem('admesh_device_id');
        localStorage.removeItem(STORAGE_KEYS.USER_PREFERENCES);
      } catch (error) {
        console.error('Failed to clear all data:', error);
      }
    }
  }

  /**
   * Check if storage is available
   */
  isStorageAvailable(): boolean {
    if (typeof window === 'undefined') return false;

    try {
      const test = 'storage_test';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Update last activity timestamp
   */
  updateActivity(): void {
    this.setSessionData({ lastActivity: Date.now() });
  }

  /**
   * Check if session is valid
   */
  isSessionValid(): boolean {
    const sessionData = this.getSessionData();
    if (!sessionData) return false;

    const timeSinceActivity = Date.now() - sessionData.lastActivity;
    return timeSinceActivity < this.SESSION_TIMEOUT;
  }
}

// Export singleton instance
export const secureStorage = SecureStorage.getInstance();

// Utility functions for common operations
export const tokenStorage = {
  set: (token: string, expiry: number) => {
    secureStorage.setTokenCache({
      token,
      expiry,
      issuedAt: Date.now(),
    });
  },
  
  get: (): string | null => {
    const cache = secureStorage.getTokenCache();
    return cache?.token || null;
  },
  
  clear: () => {
    secureStorage.clearTokenCache();
  },
  
  isValid: (): boolean => {
    const cache = secureStorage.getTokenCache();
    return cache !== null && Date.now() < cache.expiry;
  },
};

// Activity tracking
export const activityTracker = {
  update: () => secureStorage.updateActivity(),
  isValid: () => secureStorage.isSessionValid(),
  clear: () => secureStorage.clearSessionData(),
};
