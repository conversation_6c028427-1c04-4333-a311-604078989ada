/**
 * Integration tests for the enhanced authentication system
 * Tests token management, error handling, and security features
 */

import { describe, it, expect, beforeEach, afterEach, vi, Mock } from 'vitest';
import { AdvancedTokenManager } from '../auth-token-manager';
import { ApiClient } from '../api-helpers';
import { handleAuthError, AuthErrorContext } from '../auth-error-handler';
import { SecureStorage } from '../secure-storage';

// Mock Firebase auth
vi.mock('../firebase', () => ({
  auth: {
    onAuthStateChanged: vi.fn(),
    signOut: vi.fn(),
  },
}));

// Mock fetch
global.fetch = vi.fn();

// Mock localStorage and sessionStorage
const mockStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: mockStorage,
});

Object.defineProperty(window, 'sessionStorage', {
  value: mockStorage,
});

describe('Enhanced Authentication System', () => {
  let tokenManager: AdvancedTokenManager;
  let apiClient: ApiClient;
  let secureStorage: SecureStorage;

  beforeEach(() => {
    vi.clearAllMocks();
    mockStorage.getItem.mockReturnValue(null);
    
    // Reset instances
    tokenManager = new AdvancedTokenManager();
    apiClient = new ApiClient('http://localhost:8000');
    secureStorage = SecureStorage.getInstance();
  });

  afterEach(() => {
    tokenManager.destroy();
  });

  describe('Token Management', () => {
    it('should handle token refresh correctly', async () => {
      const mockUser = {
        getIdToken: vi.fn().mockResolvedValue('mock-token'),
        uid: 'test-user-id',
      };

      // Mock successful token refresh
      mockUser.getIdToken.mockResolvedValue('new-token');

      // Test token refresh
      const token = await tokenManager.refreshToken();
      expect(token).toBe('new-token');
    });

    it('should cache tokens securely', () => {
      const testToken = 'test-token';
      const expiry = Date.now() + 60000;

      secureStorage.setTokenCache({
        token: testToken,
        expiry,
        issuedAt: Date.now(),
      });

      const cached = secureStorage.getTokenCache();
      expect(cached).toBeTruthy();
      expect(cached?.token).toBe(testToken);
    });

    it('should clear expired tokens', () => {
      const testToken = 'expired-token';
      const expiry = Date.now() - 1000; // Expired

      secureStorage.setTokenCache({
        token: testToken,
        expiry,
        issuedAt: Date.now() - 2000,
      });

      const cached = secureStorage.getTokenCache();
      expect(cached).toBeNull();
    });

    it('should handle token refresh failures gracefully', async () => {
      const mockUser = {
        getIdToken: vi.fn().mockRejectedValue(new Error('Token refresh failed')),
        uid: 'test-user-id',
      };

      const token = await tokenManager.refreshToken();
      expect(token).toBeNull();
    });
  });

  describe('API Client', () => {
    it('should make authenticated requests successfully', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        json: vi.fn().mockResolvedValue({ success: true, data: { id: 1 } }),
      };

      (fetch as Mock).mockResolvedValue(mockResponse);

      const response = await apiClient.get('/test-endpoint');
      
      expect(response.success).toBe(true);
      expect(response.data).toEqual({ success: true, data: { id: 1 } });
    });

    it('should handle 401 errors with token refresh', async () => {
      // First call returns 401
      const unauthorizedResponse = {
        ok: false,
        status: 401,
        json: vi.fn().mockResolvedValue({ error: 'Unauthorized' }),
      };

      // Second call (after refresh) succeeds
      const successResponse = {
        ok: true,
        status: 200,
        json: vi.fn().mockResolvedValue({ success: true }),
      };

      (fetch as Mock)
        .mockResolvedValueOnce(unauthorizedResponse)
        .mockResolvedValueOnce(successResponse);

      const response = await apiClient.get('/protected-endpoint');
      
      // Should eventually succeed after token refresh
      expect(fetch).toHaveBeenCalledTimes(2);
    });

    it('should retry on server errors', async () => {
      const serverErrorResponse = {
        ok: false,
        status: 500,
        json: vi.fn().mockResolvedValue({ error: 'Internal Server Error' }),
      };

      const successResponse = {
        ok: true,
        status: 200,
        json: vi.fn().mockResolvedValue({ success: true }),
      };

      (fetch as Mock)
        .mockResolvedValueOnce(serverErrorResponse)
        .mockResolvedValueOnce(successResponse);

      const response = await apiClient.get('/test-endpoint');
      
      expect(fetch).toHaveBeenCalledTimes(2);
    });

    it('should handle network errors gracefully', async () => {
      (fetch as Mock).mockRejectedValue(new Error('Network error'));

      const response = await apiClient.get('/test-endpoint');
      
      expect(response.success).toBe(false);
      expect(response.error?.code).toBe('NETWORK_ERROR');
    });
  });

  describe('Error Handling', () => {
    it('should handle Firebase auth errors correctly', () => {
      const firebaseError = {
        code: 'auth/user-not-found',
        message: 'User not found',
      };

      const context: AuthErrorContext = {
        operation: 'signin',
        email: '<EMAIL>',
        provider: 'email',
      };

      const authError = handleAuthError(firebaseError, context);
      
      expect(authError.code).toBe('auth/user-not-found');
      expect(authError.type).toBe('firebase');
      expect(authError.retryable).toBe(false);
    });

    it('should handle API errors correctly', () => {
      const apiError = {
        status: 403,
        detail: 'Forbidden',
      };

      const context: AuthErrorContext = {
        operation: 'api_call',
        endpoint: '/protected-resource',
      };

      const authError = handleAuthError(apiError, context);
      
      expect(authError.code).toBe('api_forbidden');
      expect(authError.type).toBe('api');
      expect(authError.retryable).toBe(false);
    });

    it('should handle network errors correctly', () => {
      const networkError = new TypeError('Failed to fetch');

      const authError = handleAuthError(networkError);
      
      expect(authError.code).toBe('network_error');
      expect(authError.type).toBe('network');
      expect(authError.retryable).toBe(true);
    });
  });

  describe('Secure Storage', () => {
    it('should encrypt and decrypt data correctly', () => {
      const testData = 'sensitive-data';
      
      secureStorage.setTokenCache({
        token: testData,
        expiry: Date.now() + 60000,
        issuedAt: Date.now(),
      });

      // Verify data is stored (should be encrypted)
      expect(mockStorage.setItem).toHaveBeenCalled();
      
      // Verify data can be retrieved and decrypted
      const retrieved = secureStorage.getTokenCache();
      expect(retrieved?.token).toBe(testData);
    });

    it('should handle storage errors gracefully', () => {
      mockStorage.setItem.mockImplementation(() => {
        throw new Error('Storage quota exceeded');
      });

      // Should not throw error
      expect(() => {
        secureStorage.setTokenCache({
          token: 'test',
          expiry: Date.now() + 60000,
          issuedAt: Date.now(),
        });
      }).not.toThrow();
    });

    it('should validate session activity', () => {
      // Set recent activity
      secureStorage.setSessionData({ lastActivity: Date.now() });
      expect(secureStorage.isSessionValid()).toBe(true);

      // Set old activity
      secureStorage.setSessionData({ lastActivity: Date.now() - 25 * 60 * 60 * 1000 });
      expect(secureStorage.isSessionValid()).toBe(false);
    });
  });

  describe('Integration Scenarios', () => {
    it('should handle complete authentication flow', async () => {
      // Mock successful authentication
      const mockUser = {
        getIdToken: vi.fn().mockResolvedValue('auth-token'),
        uid: 'user-123',
      };

      // Mock successful API response
      const mockApiResponse = {
        ok: true,
        status: 200,
        json: vi.fn().mockResolvedValue({
          uid: 'user-123',
          email: '<EMAIL>',
          role: 'user',
        }),
      };

      (fetch as Mock).mockResolvedValue(mockApiResponse);

      // Test the flow
      const token = await tokenManager.getToken();
      expect(token).toBeTruthy();

      const response = await apiClient.get('/api/auth/me');
      expect(response.success).toBe(true);
    });

    it('should handle token expiration and refresh cycle', async () => {
      // Set up expired token
      secureStorage.setTokenCache({
        token: 'expired-token',
        expiry: Date.now() - 1000,
        issuedAt: Date.now() - 2000,
      });

      const mockUser = {
        getIdToken: vi.fn().mockResolvedValue('new-token'),
        uid: 'user-123',
      };

      // Should automatically refresh token
      const token = await tokenManager.getToken();
      expect(token).toBe('new-token');
    });
  });
});
