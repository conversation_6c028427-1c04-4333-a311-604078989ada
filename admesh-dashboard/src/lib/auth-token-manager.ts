/**
 * Advanced Firebase token management with automatic refresh and background monitoring
 */

import { auth } from '@/lib/firebase';
import { User, onAuthStateChanged } from 'firebase/auth';
import { secureStorage, tokenStorage, activityTracker } from '@/lib/secure-storage';

export interface TokenInfo {
  token: string;
  expiry: number;
  issuedAt: number;
}

export interface TokenManagerEvents {
  tokenRefreshed: (token: string) => void;
  tokenExpired: () => void;
  refreshFailed: (error: Error) => void;
  userSignedOut: () => void;
}

export class AdvancedTokenManager {
  private currentUser: User | null = null;
  private tokenInfo: TokenInfo | null = null;
  private refreshPromise: Promise<string | null> | null = null;
  private refreshTimer: NodeJS.Timeout | null = null;
  private eventListeners: Partial<TokenManagerEvents> = {};
  private isInitialized = false;

  // Configuration
  private readonly REFRESH_BUFFER_TIME = 5 * 60 * 1000; // 5 minutes before expiry
  private readonly MIN_REFRESH_INTERVAL = 30 * 1000; // Minimum 30 seconds between refreshes
  private readonly MAX_RETRY_ATTEMPTS = 3;
  private readonly RETRY_DELAY_BASE = 1000; // Base delay for exponential backoff

  constructor() {
    this.initialize();
  }

  /**
   * Initialize the token manager
   */
  private initialize(): void {
    if (this.isInitialized) return;

    onAuthStateChanged(auth, (user) => {
      this.handleAuthStateChange(user);
    });

    // Handle page visibility changes to refresh tokens when page becomes visible
    if (typeof document !== 'undefined') {
      document.addEventListener('visibilitychange', () => {
        if (!document.hidden && this.currentUser) {
          this.checkAndRefreshToken();
        }
      });
    }

    this.isInitialized = true;
  }

  /**
   * Handle Firebase auth state changes
   */
  private handleAuthStateChange(user: User | null): void {
    const wasSignedIn = !!this.currentUser;
    this.currentUser = user;

    if (!user) {
      // User signed out
      this.clearTokenInfo();
      this.clearRefreshTimer();
      if (wasSignedIn) {
        this.emit('userSignedOut');
      }
    } else {
      // User signed in or token changed
      this.scheduleTokenRefresh();
    }
  }

  /**
   * Get current valid token
   */
  async getToken(forceRefresh = false): Promise<string | null> {
    console.log('TokenManager: getToken called', {
      hasUser: !!this.currentUser,
      forceRefresh,
      hasTokenInfo: !!this.tokenInfo
    });

    if (!this.currentUser) {
      console.log('TokenManager: No current user, returning null');
      return null;
    }

    // Update activity tracking
    activityTracker.update();

    // Check secure storage first if no in-memory token
    if (!this.tokenInfo && !forceRefresh) {
      const cachedToken = tokenStorage.get();
      if (cachedToken && tokenStorage.isValid()) {
        // Restore token info from secure storage
        const cache = secureStorage.getTokenCache();
        if (cache) {
          this.tokenInfo = {
            token: cache.token,
            expiry: cache.expiry,
            issuedAt: cache.issuedAt,
          };
          this.scheduleTokenRefresh();
          return cache.token;
        }
      }
    }

    // If force refresh or no cached token, get fresh token
    if (forceRefresh || !this.tokenInfo) {
      return this.refreshToken();
    }

    // Check if current token is still valid
    const now = Date.now();
    const timeUntilExpiry = this.tokenInfo.expiry - now;

    // If token expires soon, refresh it
    if (timeUntilExpiry <= this.REFRESH_BUFFER_TIME) {
      return this.refreshToken();
    }

    return this.tokenInfo.token;
  }

  /**
   * Refresh the authentication token
   */
  async refreshToken(): Promise<string | null> {
    if (!this.currentUser) {
      return null;
    }

    // Prevent multiple simultaneous refresh attempts
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    this.refreshPromise = this.performTokenRefresh();
    
    try {
      const token = await this.refreshPromise;
      return token;
    } finally {
      this.refreshPromise = null;
    }
  }

  /**
   * Perform the actual token refresh with retry logic
   */
  private async performTokenRefresh(attempt = 1): Promise<string | null> {
    try {
      if (!this.currentUser) {
        return null;
      }

      console.log(`Refreshing Firebase token (attempt ${attempt}/${this.MAX_RETRY_ATTEMPTS})`);
      
      // Get fresh token from Firebase
      const token = await this.currentUser.getIdToken(true);
      
      // Parse token to get expiry time
      const tokenPayload = this.parseJwtPayload(token);
      const expiry = tokenPayload?.exp ? (tokenPayload.exp as number) * 1000 : Date.now() + (60 * 60 * 1000);
      
      // Update token info
      this.tokenInfo = {
        token,
        expiry,
        issuedAt: Date.now(),
      };

      // Store in secure storage
      tokenStorage.set(token, expiry);

      // Schedule next refresh
      this.scheduleTokenRefresh();

      // Emit event
      this.emit('tokenRefreshed', token);

      console.log('Firebase token refreshed successfully');
      return token;

    } catch (error) {
      console.error(`Token refresh failed (attempt ${attempt}):`, error);

      // Retry with exponential backoff
      if (attempt < this.MAX_RETRY_ATTEMPTS) {
        const delay = this.RETRY_DELAY_BASE * Math.pow(2, attempt - 1);
        await this.delay(delay);
        return this.performTokenRefresh(attempt + 1);
      }

      // All retries failed
      this.emit('refreshFailed', error as Error);
      this.clearTokenInfo();
      return null;
    }
  }

  /**
   * Schedule automatic token refresh
   */
  private scheduleTokenRefresh(): void {
    this.clearRefreshTimer();

    if (!this.tokenInfo) {
      return;
    }

    const now = Date.now();
    const timeUntilRefresh = this.tokenInfo.expiry - now - this.REFRESH_BUFFER_TIME;

    // Don't schedule if refresh time is too soon or already passed
    if (timeUntilRefresh <= this.MIN_REFRESH_INTERVAL) {
      // Refresh immediately if needed
      if (timeUntilRefresh <= 0) {
        this.refreshToken();
      }
      return;
    }

    console.log(`Scheduling token refresh in ${Math.round(timeUntilRefresh / 1000)} seconds`);

    this.refreshTimer = setTimeout(() => {
      this.refreshToken();
    }, timeUntilRefresh);
  }

  /**
   * Check if token needs refresh and refresh if necessary
   */
  private async checkAndRefreshToken(): Promise<void> {
    if (!this.tokenInfo || !this.currentUser) {
      return;
    }

    const now = Date.now();
    const timeUntilExpiry = this.tokenInfo.expiry - now;

    if (timeUntilExpiry <= this.REFRESH_BUFFER_TIME) {
      await this.refreshToken();
    }
  }

  /**
   * Parse JWT payload
   */
  private parseJwtPayload(token: string): Record<string, unknown> | null {
    try {
      const payload = token.split('.')[1];
      return JSON.parse(atob(payload));
    } catch {
      return null;
    }
  }

  /**
   * Check if token is expired
   */
  isTokenExpired(token?: string): boolean {
    const tokenToCheck = token || this.tokenInfo?.token;
    if (!tokenToCheck) return true;

    const payload = this.parseJwtPayload(tokenToCheck);
    if (!payload?.exp) return true;

    return Date.now() >= (payload.exp as number) * 1000;
  }

  /**
   * Get token expiry time
   */
  getTokenExpiry(): number | null {
    return this.tokenInfo?.expiry || null;
  }

  /**
   * Clear token information
   */
  private clearTokenInfo(): void {
    this.tokenInfo = null;
    tokenStorage.clear();
  }

  /**
   * Clear refresh timer
   */
  private clearRefreshTimer(): void {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }
  }

  /**
   * Add event listener
   */
  on<K extends keyof TokenManagerEvents>(event: K, listener: TokenManagerEvents[K]): void {
    this.eventListeners[event] = listener;
  }

  /**
   * Remove event listener
   */
  off<K extends keyof TokenManagerEvents>(event: K): void {
    delete this.eventListeners[event];
  }

  /**
   * Emit event
   */
  private emit<K extends keyof TokenManagerEvents>(event: K, ...args: Parameters<NonNullable<TokenManagerEvents[K]>>): void {
    const listener = this.eventListeners[event];
    if (listener) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (listener as any)(...args);
    }
  }

  /**
   * Utility delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    this.clearRefreshTimer();
    this.eventListeners = {};
    this.tokenInfo = null;
    this.refreshPromise = null;
    secureStorage.clearAll();
  }
}

// Export singleton instance
export const tokenManager = new AdvancedTokenManager();

// Event listener utilities for external use
export const tokenEventListeners = {
  onTokenRefreshed: (callback: (token: string) => void) => {
    tokenManager.on('tokenRefreshed', callback);
  },
  onTokenExpired: (callback: () => void) => {
    tokenManager.on('tokenExpired', callback);
  },
  onRefreshFailed: (callback: (error: Error) => void) => {
    tokenManager.on('refreshFailed', callback);
  },
  onUserSignedOut: (callback: () => void) => {
    tokenManager.on('userSignedOut', callback);
  },
  removeListener: (event: keyof TokenManagerEvents) => {
    tokenManager.off(event);
  },
};
