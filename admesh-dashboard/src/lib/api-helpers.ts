/**
 * Enhanced API client for making authenticated requests to the backend API
 * Includes automatic token management, refresh, and retry logic
 */

import { tokenManager } from '@/lib/auth-token-manager';

// Types for API responses and errors
export interface ApiError {
  detail?: string;
  message?: string;
  code?: string;
  status?: number;
}

export interface ApiResponse<T = unknown> {
  data?: T;
  error?: ApiError;
  success: boolean;
}

// Note: Token management is now handled by the AdvancedTokenManager in auth-token-manager.ts

// Enhanced API client class
export class ApiClient {
  private baseUrl: string;
  private defaultHeaders: Record<string, string>;
  private maxRetries: number;
  private retryDelay: number;

  constructor(
    baseUrl: string = process.env.NEXT_PUBLIC_API_BASE_URL || '',
    options: {
      maxRetries?: number;
      retryDelay?: number;
      defaultHeaders?: Record<string, string>;
    } = {}
  ) {
    this.baseUrl = baseUrl.replace(/\/$/, ''); // Remove trailing slash
    this.maxRetries = options.maxRetries ?? 3;
    this.retryDelay = options.retryDelay ?? 1000;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      ...options.defaultHeaders,
    };
  }

  /**
   * Make an authenticated API request with automatic token management and retry logic
   */
  async request<T = unknown>(
    endpoint: string,
    options: RequestInit & {
      skipAuth?: boolean;
      skipRetry?: boolean;
    } = {}
  ): Promise<ApiResponse<T>> {
    const { skipAuth = false, skipRetry = false, ...fetchOptions } = options;
    const url = `${this.baseUrl}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;

    const headers = { ...this.defaultHeaders, ...fetchOptions.headers } as Record<string, string>;

    // Add authentication header if not skipped
    if (!skipAuth) {
      const token = await tokenManager.getToken();
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      } else {
        console.error('API Client: No authentication token available', {
          endpoint,
          timestamp: new Date().toISOString()
        });
        return {
          success: false,
          error: {
            code: 'NO_AUTH_TOKEN',
            message: 'No authentication token available. Please sign in.',
            status: 401,
          },
        };
      }
    }

    const requestOptions: RequestInit = {
      ...fetchOptions,
      headers,
    };

    return this._makeRequestWithRetry<T>(url, requestOptions, skipRetry);
  }

  /**
   * Make a request with retry logic for token expiration and network errors
   */
  private async _makeRequestWithRetry<T>(
    url: string,
    options: RequestInit,
    skipRetry: boolean,
    attempt: number = 1
  ): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(url, options);

      // Handle token expiration (401) with automatic refresh and retry
      if (response.status === 401 && !skipRetry && attempt === 1) {
        console.log('Token expired, attempting refresh...');

        const newToken = await tokenManager.refreshToken();
        if (newToken) {
          // Retry with new token
          const newHeaders = {
            ...options.headers,
            Authorization: `Bearer ${newToken}`,
          };

          return this._makeRequestWithRetry<T>(
            url,
            { ...options, headers: newHeaders },
            true, // Skip retry on second attempt
            2
          );
        } else {
          return {
            success: false,
            error: {
              code: 'TOKEN_REFRESH_FAILED',
              message: 'Failed to refresh authentication token. Please sign in again.',
              status: 401,
            },
          };
        }
      }

      // Handle other errors
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));

        // Retry on server errors (5xx) and rate limits (429)
        if (!skipRetry && attempt < this.maxRetries &&
            (response.status >= 500 || response.status === 429)) {
          console.log(`Request failed with status ${response.status}, retrying... (attempt ${attempt}/${this.maxRetries})`);

          await this._delay(this.retryDelay * attempt);
          return this._makeRequestWithRetry<T>(url, options, skipRetry, attempt + 1);
        }

        // Debug logging for error responses
        if (process.env.NODE_ENV === 'development' && url.includes('/api/auth/me')) {
          console.log('🔍 API Error Response Debug:', {
            url,
            status: response.status,
            statusText: response.statusText,
            errorData,
            hasErrorData: !!errorData,
            errorDataKeys: errorData && typeof errorData === 'object' ? Object.keys(errorData) : 'N/A'
          });
        }

        return {
          success: false,
          error: {
            detail: errorData.detail,
            message: errorData.message || errorData.detail || `Request failed with status ${response.status}`,
            code: errorData.code,
            status: response.status,
          },
        };
      }

      // Success case
      const data = await response.json().catch((jsonError) => {
        console.warn('Failed to parse JSON response:', jsonError);
        return null;
      });

      // Note: Debug logging removed to reduce console noise

      return {
        success: true,
        data,
      };

    } catch (error) {
      // Network errors and other exceptions
      if (!skipRetry && attempt < this.maxRetries) {
        console.log(`Network error, retrying... (attempt ${attempt}/${this.maxRetries})`, error);

        await this._delay(this.retryDelay * attempt);
        return this._makeRequestWithRetry<T>(url, options, skipRetry, attempt + 1);
      }

      return {
        success: false,
        error: {
          code: 'NETWORK_ERROR',
          message: error instanceof Error ? error.message : 'Network request failed',
          status: 0,
        },
      };
    }
  }

  /**
   * Utility method for delays
   */
  private _delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Convenience methods for common HTTP verbs

  /**
   * Make a GET request
   */
  async get<T = unknown>(endpoint: string, options: Omit<RequestInit, 'method'> & { skipAuth?: boolean } = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'GET' });
  }

  /**
   * Make a POST request
   */
  async post<T = unknown>(
    endpoint: string,
    data?: unknown,
    options: Omit<RequestInit, 'method' | 'body'> & { skipAuth?: boolean } = {}
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * Make a PUT request
   */
  async put<T = unknown>(
    endpoint: string,
    data?: unknown,
    options: Omit<RequestInit, 'method' | 'body'> & { skipAuth?: boolean } = {}
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * Make a PATCH request
   */
  async patch<T = unknown>(
    endpoint: string,
    data?: unknown,
    options: Omit<RequestInit, 'method' | 'body'> & { skipAuth?: boolean } = {}
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * Make a DELETE request
   */
  async delete<T = unknown>(endpoint: string, options: Omit<RequestInit, 'method'> & { skipAuth?: boolean } = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'DELETE' });
  }

  /**
   * Check if the current user is authenticated
   */
  async isAuthenticated(): Promise<boolean> {
    const token = await tokenManager.getToken();
    return token !== null;
  }

  /**
   * Force refresh the authentication token
   */
  async refreshAuthToken(): Promise<boolean> {
    const token = await tokenManager.refreshToken();
    return token !== null;
  }
}

// Create and export a default API client instance
export const apiClient = new ApiClient();

// Legacy functions for backward compatibility
/**
 * @deprecated Use apiClient.request() instead
 */
export async function fetchWithAuth(
  url: string,
  options: RequestInit = {}
): Promise<Response> {
  console.warn('fetchWithAuth is deprecated. Use apiClient.request() instead.');
  const response = await fetch(url, options);
  return response;
}

/**
 * @deprecated Use apiClient.get() or apiClient.post() instead
 */
export async function fetchJsonWithAuth<T>(
  url: string,
  options: RequestInit = {}
): Promise<T> {
  console.warn('fetchJsonWithAuth is deprecated. Use apiClient methods instead.');

  const response = await fetchWithAuth(url, {
    ...options,
    headers: {
      ...options.headers,
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.detail || `API request failed with status ${response.status}`
    );
  }

  return response.json();
}
