/**
 * Enhanced authentication error handling system
 * Provides comprehensive error messages and handling for Firebase auth and API errors
 */

import { FirebaseError } from 'firebase/app';

export interface AuthError {
  code: string;
  message: string;
  type: 'firebase' | 'api' | 'network' | 'validation';
  severity: 'error' | 'warning' | 'info';
  retryable: boolean;
  userAction?: string;
}

export interface AuthErrorContext {
  operation: 'signin' | 'signup' | 'signout' | 'token_refresh' | 'api_call';
  email?: string;
  provider?: 'email' | 'google';
  endpoint?: string;
}

/**
 * Enhanced Firebase error handler with detailed user-friendly messages
 */
export function handleFirebaseAuthError(error: unknown, _context?: AuthErrorContext): AuthError {
  const firebaseError = error as FirebaseError;
  const errorCode = firebaseError?.code || 'unknown';

  // Firebase Auth error mappings
  const firebaseErrorMap: Record<string, Omit<AuthError, 'code'>> = {
    // Sign-in errors
    'auth/user-not-found': {
      message: 'No account found with this email address. Please check your email or create a new account.',
      type: 'firebase',
      severity: 'error',
      retryable: false,
      userAction: 'Verify email address or sign up for a new account',
    },
    'auth/wrong-password': {
      message: 'Incorrect password. Please try again or reset your password.',
      type: 'firebase',
      severity: 'error',
      retryable: true,
      userAction: 'Check password or use "Forgot Password" link',
    },
    'auth/invalid-credential': {
      message: 'Invalid email or password. Please check your credentials and try again.',
      type: 'firebase',
      severity: 'error',
      retryable: true,
      userAction: 'Verify email and password are correct',
    },
    'auth/invalid-email': {
      message: 'Please enter a valid email address.',
      type: 'validation',
      severity: 'error',
      retryable: true,
      userAction: 'Enter a properly formatted email address',
    },
    'auth/user-disabled': {
      message: 'This account has been disabled. Please contact support for assistance.',
      type: 'firebase',
      severity: 'error',
      retryable: false,
      userAction: 'Contact customer support',
    },
    'auth/too-many-requests': {
      message: 'Too many failed sign-in attempts. Please try again later or reset your password.',
      type: 'firebase',
      severity: 'warning',
      retryable: true,
      userAction: 'Wait a few minutes before trying again',
    },

    // Sign-up errors
    'auth/email-already-in-use': {
      message: 'An account with this email already exists. Please sign in instead.',
      type: 'firebase',
      severity: 'error',
      retryable: false,
      userAction: 'Use the sign-in form instead',
    },
    'auth/weak-password': {
      message: 'Password is too weak. Please choose a stronger password with at least 6 characters.',
      type: 'validation',
      severity: 'error',
      retryable: true,
      userAction: 'Create a stronger password',
    },

    // Google sign-in errors
    'auth/popup-closed-by-user': {
      message: 'Sign-in was cancelled. Please try again.',
      type: 'firebase',
      severity: 'info',
      retryable: true,
      userAction: 'Complete the Google sign-in process',
    },
    'auth/popup-blocked': {
      message: 'Pop-up was blocked by your browser. Please allow pop-ups for this site and try again.',
      type: 'firebase',
      severity: 'warning',
      retryable: true,
      userAction: 'Enable pop-ups in browser settings',
    },
    'auth/cancelled-popup-request': {
      message: 'Sign-in was cancelled. Please try again.',
      type: 'firebase',
      severity: 'info',
      retryable: true,
      userAction: 'Try the sign-in process again',
    },
    'auth/account-exists-with-different-credential': {
      message: 'An account already exists with this email using a different sign-in method.',
      type: 'firebase',
      severity: 'error',
      retryable: false,
      userAction: 'Try signing in with email/password instead',
    },

    // Network and technical errors
    'auth/network-request-failed': {
      message: 'Network error. Please check your internet connection and try again.',
      type: 'network',
      severity: 'error',
      retryable: true,
      userAction: 'Check internet connection and retry',
    },
    'auth/internal-error': {
      message: 'An internal error occurred. Please try again.',
      type: 'firebase',
      severity: 'error',
      retryable: true,
      userAction: 'Try again in a few moments',
    },
    'auth/operation-not-allowed': {
      message: 'This sign-in method is not enabled. Please contact support.',
      type: 'firebase',
      severity: 'error',
      retryable: false,
      userAction: 'Contact customer support',
    },

    // Token-related errors
    'auth/id-token-expired': {
      message: 'Your session has expired. Please sign in again.',
      type: 'firebase',
      severity: 'warning',
      retryable: true,
      userAction: 'Sign in again',
    },
    'auth/id-token-revoked': {
      message: 'Your session has been revoked. Please sign in again.',
      type: 'firebase',
      severity: 'warning',
      retryable: true,
      userAction: 'Sign in again',
    },
  };

  const errorInfo = firebaseErrorMap[errorCode];

  if (errorInfo) {
    return {
      code: errorCode,
      ...errorInfo,
    };
  }

  // Fallback for unknown Firebase errors
  return {
    code: errorCode,
    message: firebaseError?.message || 'An unexpected error occurred. Please try again.',
    type: 'firebase',
    severity: 'error',
    retryable: true,
    userAction: 'Try again or contact support if the problem persists',
  };
}

/**
 * Handle API errors from the backend
 */
export function handleApiError(error: unknown, _context?: AuthErrorContext): AuthError {
  const errorObj = error as Record<string, unknown>;
  const status = (errorObj?.status as number) || (errorObj?.response as Record<string, unknown>)?.status as number || 0;
  const detail = (errorObj?.detail as string) || (errorObj?.message as string) || 'Unknown API error';
  const code = (errorObj?.code as string) || `api_error_${status}`;

  // API error mappings
  if (status === 401) {
    return {
      code: 'api_unauthorized',
      message: 'Authentication failed. Please sign in again.',
      type: 'api',
      severity: 'error',
      retryable: true,
      userAction: 'Sign in again',
    };
  }

  if (status === 403) {
    return {
      code: 'api_forbidden',
      message: 'You do not have permission to perform this action.',
      type: 'api',
      severity: 'error',
      retryable: false,
      userAction: 'Contact support if you believe this is an error',
    };
  }

  if (status === 429) {
    return {
      code: 'api_rate_limit',
      message: 'Too many requests. Please wait a moment and try again.',
      type: 'api',
      severity: 'warning',
      retryable: true,
      userAction: 'Wait a few seconds before trying again',
    };
  }

  if (status >= 500) {
    return {
      code: 'api_server_error',
      message: 'Server error. Please try again later.',
      type: 'api',
      severity: 'error',
      retryable: true,
      userAction: 'Try again in a few minutes',
    };
  }

  if (status === 0) {
    return {
      code: 'network_error',
      message: 'Network connection failed. Please check your internet connection.',
      type: 'network',
      severity: 'error',
      retryable: true,
      userAction: 'Check internet connection and retry',
    };
  }

  // Generic API error
  return {
    code,
    message: detail,
    type: 'api',
    severity: 'error',
    retryable: status >= 500 || status === 429,
    userAction: 'Try again or contact support if the problem persists',
  };
}

/**
 * Handle network errors
 */
export function handleNetworkError(_error: unknown, _context?: AuthErrorContext): AuthError {
  return {
    code: 'network_error',
    message: 'Network connection failed. Please check your internet connection and try again.',
    type: 'network',
    severity: 'error',
    retryable: true,
    userAction: 'Check internet connection and retry',
  };
}

/**
 * Main error handler that determines error type and routes to appropriate handler
 */
export function handleAuthError(error: unknown, context?: AuthErrorContext): AuthError {
  const errorObj = error as Record<string, unknown>;

  // Firebase errors
  if (typeof errorObj?.code === 'string' && errorObj.code.startsWith('auth/')) {
    return handleFirebaseAuthError(error, context);
  }

  // API errors (from our backend)
  if (errorObj?.status || (errorObj?.response as Record<string, unknown>)?.status) {
    return handleApiError(error, context);
  }

  // Network errors
  if (error instanceof TypeError && error.message.includes('fetch')) {
    return handleNetworkError(error, context);
  }

  // Generic error fallback
  return {
    code: 'unknown_error',
    message: (errorObj?.message as string) || 'An unexpected error occurred. Please try again.',
    type: 'firebase',
    severity: 'error',
    retryable: true,
    userAction: 'Try again or contact support if the problem persists',
  };
}

/**
 * Format error for display in UI
 */
export function formatErrorForDisplay(authError: AuthError): {
  title: string;
  message: string;
  action?: string;
} {
  const severityTitles = {
    error: 'Error',
    warning: 'Warning',
    info: 'Information',
  };

  return {
    title: severityTitles[authError.severity],
    message: authError.message,
    action: authError.userAction,
  };
}

/**
 * Check if an error should trigger a retry
 */
export function shouldRetryError(authError: AuthError): boolean {
  return authError.retryable && authError.type !== 'validation';
}
