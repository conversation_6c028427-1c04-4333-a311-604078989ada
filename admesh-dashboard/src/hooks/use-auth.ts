// hooks/use-auth.ts
import { useEffect, useState, useCallback, useRef } from "react";
import { onAuthStateChanged, User, getIdTokenResult } from "firebase/auth";
import { auth } from "@/lib/firebase";
import { tokenManager } from "@/lib/auth-token-manager";
import { apiClient } from "@/lib/api-helpers";
import { activityTracker } from "@/lib/secure-storage";
import { logAuthDebugInfo } from "@/lib/debug-auth";

// Cache duration in milliseconds (5 minutes)
const CACHE_DURATION = 5 * 60 * 1000;

// Interface for user data from API
interface UserData {
  uid: string;
  email?: string;
  name?: string;
  onboardingStatus?: string;
  seenPioneerWelcome?: boolean;
  xp?: number;
  lifetime_xp?: number;
  onboarding_status?: string;
  onboarding_steps?: {
    brand?: boolean;
    product?: boolean;
    offer?: boolean;
    tracking?: boolean;
  };
  onboarding_completed?: boolean;
  [key: string]: unknown; // Allow for other properties
}

// Interface for cached user data
interface CachedUserData {
  data: UserData;
  timestamp: number;
}

// Global cache to persist between component renders
const userDataCache: Record<string, CachedUserData> = {};

export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [role, setRole] = useState<"user" | "agent" | "brand" | "admin" | "">("");
  const [isAdmin, setIsAdmin] = useState<boolean>(false);
  const [onboardingStatus, setOnboardingStatus] = useState<string | null>(null);
  const [seenPioneerWelcome, setSeenPioneerWelcome] = useState<boolean>(false);
  const [xp, setXp] = useState<number>(0);
  const [lifetimeXp, setLifetimeXp] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Ref to track if a fetch is in progress to prevent duplicate calls
  const isFetchingRef = useRef(false);
  // Ref to store the last fetch timestamp
  const lastFetchTimestampRef = useRef(0);

  // Function to fetch user data with enhanced API client
  const fetchUserData = useCallback(async (currentUser: User, _customRole: string, forceRefresh = false) => {
    if (!currentUser) return null;

    const uid = currentUser.uid;
    const now = Date.now();

    // Check if we're already fetching data
    if (isFetchingRef.current) {
      return null;
    }

    // Check if we have cached data and it's still valid
    if (!forceRefresh &&
        userDataCache[uid] &&
        now - userDataCache[uid].timestamp < CACHE_DURATION) {
      return userDataCache[uid].data;
    }

    // Prevent duplicate fetches
    isFetchingRef.current = true;

    try {
      // Use enhanced API client with automatic token management
      const response = await apiClient.get('/api/auth/me');

      if (response.success && response.data) {
        const userData = response.data;

        // Update the cache
        userDataCache[uid] = {
          data: userData,
          timestamp: now
        };

        // Update last fetch timestamp
        lastFetchTimestampRef.current = now;

        return userData;
      }

      if (response.error) {
        console.error("API error fetching user data:", {
          error: response.error,
          endpoint: '/api/auth/me',
          uid: uid,
          timestamp: new Date().toISOString(),
          errorDetails: {
            code: response.error.code,
            message: response.error.message,
            status: response.error.status,
            detail: response.error.detail
          }
        });

        // Run debug info to help troubleshoot
        if (process.env.NODE_ENV === 'development') {
          console.log('🔍 Running auth debug due to API error...');
          logAuthDebugInfo();
        }
      } else {
        console.error("Unknown API error fetching user data:", {
          response,
          endpoint: '/api/auth/me',
          uid: uid,
          timestamp: new Date().toISOString()
        });

        // Run debug info to help troubleshoot
        if (process.env.NODE_ENV === 'development') {
          console.log('🔍 Running auth debug due to unknown API error...');
          logAuthDebugInfo();
        }
      }

      return null;
    } catch (error) {
      console.error("Network/unexpected error fetching user data:", {
        error: error instanceof Error ? error.message : error,
        stack: error instanceof Error ? error.stack : undefined,
        endpoint: '/api/auth/me',
        uid: uid,
        timestamp: new Date().toISOString()
      });
      return null;
    } finally {
      isFetchingRef.current = false;
    }
  }, []);

  // Function to update user state from data
  const updateUserState = useCallback((userData: UserData, customRole: string) => {
    if (!userData) return;

    // Set user-specific onboarding fields
    if (customRole === "user") {
      setOnboardingStatus(userData.onboardingStatus || "pending");
      setSeenPioneerWelcome(userData.seenPioneerWelcome || false);
      setXp(userData.xp || 0);
      setLifetimeXp(userData.lifetime_xp || userData.xp || 0);
    }

    // Handle brand onboarding status
    if (customRole === "brand") {
      // Check if onboarding_steps exists and tracking is true
      const onboardingSteps = userData.onboarding_steps as { brand?: boolean; product?: boolean; offer?: boolean; tracking?: boolean } | undefined;

      if (onboardingSteps && onboardingSteps.tracking === true) {
        // If tracking step is completed, set onboarding status to "completed"
        setOnboardingStatus("completed");
      } else if (userData.onboarding_status) {
        // Otherwise use the provided onboarding_status
        setOnboardingStatus(userData.onboarding_status as string);
      }
    }
  }, []);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (currentUser) => {
      setUser(currentUser);
      setIsLoading(true);

      if (currentUser) {
        try {
          // Update activity tracking
          activityTracker.update();

          // Get token claims with enhanced token management
          const tokenResult = await getIdTokenResult(currentUser, true); // Force refresh
          const claims = tokenResult.claims;
          const customRole = claims.role as "user" | "agent" | "brand" | undefined;

          // Check if user is an admin
          const adminStatus = claims.admin as boolean | undefined;
          setIsAdmin(!!adminStatus);

          // If user is an admin, set role to admin regardless of other role
          if (adminStatus) {
            setRole("admin");
          } else if (customRole) {
            setRole(customRole);
          } else {
            setRole(""); // No role set in custom claims
          }

          // Check onboarding status for brands
          if (customRole === "brand" && !adminStatus) {
            // If we have onboarding_status in claims, use it
            const claimOnboardingStatus = claims.onboarding_status as string | undefined;
            if (claimOnboardingStatus) {
              setOnboardingStatus(claimOnboardingStatus);
            }
          } else {
            setOnboardingStatus(null); // Not relevant for non-brand roles or admins
          }

          // Fetch additional user data from the backend
          try {
            const userData = await fetchUserData(currentUser, customRole || "");
            if (userData) {
              updateUserState(userData, customRole || "");
            }
          } catch (fetchError) {
            console.error("Error fetching user data:", fetchError);
          }
        } catch (error) {
          console.error("Error fetching custom claims:", error);
          setRole(""); // Handle gracefully
          setIsAdmin(false);
        } finally {
          setIsLoading(false);
        }
      } else {
        // Reset all states on logout and clear secure storage
        setRole("");
        setIsAdmin(false);
        setOnboardingStatus(null);
        setSeenPioneerWelcome(false);
        setXp(0);
        setLifetimeXp(0);
        setIsLoading(false);

        // Clear activity tracking
        activityTracker.clear();
      }
    });

    return () => unsubscribe();
  }, [fetchUserData, updateUserState]);

  // Function to refresh user data
  const refreshUser = useCallback(async () => {
    if (!user) return;

    try {
      setIsLoading(true);

      // Force refresh token to get updated claims
      const tokenResult = await getIdTokenResult(user, true);
      const claims = tokenResult.claims;
      const customRole = claims.role as "user" | "agent" | "brand" | undefined;

      // Check onboarding status for brands in claims
      if (customRole === "brand") {
        // If we have onboarding_status in claims, use it
        const claimOnboardingStatus = claims.onboarding_status as string | undefined;
        if (claimOnboardingStatus) {
          setOnboardingStatus(claimOnboardingStatus);
        }
      }

      if (customRole) {
        // Force refresh of user data
        const userData = await fetchUserData(user, customRole, true);
        if (userData) {
          updateUserState(userData, customRole);
        }
      }
    } catch (error) {
      console.error("Error refreshing user data:", error);
    } finally {
      setIsLoading(false);
    }
  }, [user, fetchUserData, updateUserState]);

  // Function to get current auth token
  const getAuthToken = useCallback(async (forceRefresh = false): Promise<string | null> => {
    if (!user) return null;

    try {
      return await tokenManager.getToken(forceRefresh);
    } catch (error) {
      console.error("Error getting auth token:", error);
      return null;
    }
  }, [user]);

  // Function to check if user is authenticated
  const isAuthenticated = useCallback((): boolean => {
    return !!user && activityTracker.isValid();
  }, [user]);

  // Function to sign out with cleanup
  const signOut = useCallback(async (): Promise<void> => {
    try {
      await auth.signOut();
      // Cleanup is handled in the auth state change listener
    } catch (error) {
      console.error("Error signing out:", error);
    }
  }, []);

  return {
    user,
    role,
    isAdmin,
    onboardingStatus,
    seenPioneerWelcome,
    xp,
    lifetimeXp,
    isLoading,
    refreshUser,
    getAuthToken,
    isAuthenticated,
    signOut,
  };
}
