'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { apiClient } from '@/lib/api-helpers';
import { useAuth } from '@/hooks/use-auth';

export function AuthTest() {
  const [testResult, setTestResult] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useAuth();

  const testApiCall = async () => {
    setIsLoading(true);
    setTestResult(null);

    try {
      console.log('🧪 Testing API call to /api/auth/me');
      const response = await apiClient.get('/api/auth/me');
      
      console.log('🧪 API Response:', response);
      setTestResult({
        success: response.success,
        hasData: !!response.data,
        dataKeys: response.data ? Object.keys(response.data) : [],
        error: response.error,
        fullResponse: response
      });
    } catch (error) {
      console.error('🧪 API Test Error:', error);
      setTestResult({
        success: false,
        error: error instanceof Error ? error.message : String(error),
        fullResponse: null
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!user) {
    return (
      <Card className="w-96">
        <CardHeader>
          <CardTitle>Auth Test</CardTitle>
        </CardHeader>
        <CardContent>
          <p>No user signed in</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-96">
      <CardHeader>
        <CardTitle>Auth API Test</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <p><strong>User:</strong> {user.email}</p>
          <p><strong>UID:</strong> {user.uid}</p>
        </div>
        
        <Button onClick={testApiCall} disabled={isLoading}>
          {isLoading ? 'Testing...' : 'Test /api/auth/me'}
        </Button>

        {testResult && (
          <div className="mt-4 p-4 bg-gray-100 rounded text-sm">
            <h4 className="font-semibold mb-2">Test Result:</h4>
            <div className="space-y-2">
              <div>Success: <span className={testResult.success ? 'text-green-600' : 'text-red-600'}>
                {testResult.success ? 'Yes' : 'No'}
              </span></div>
              
              {testResult.hasData !== undefined && (
                <div>Has Data: <span className={testResult.hasData ? 'text-green-600' : 'text-red-600'}>
                  {testResult.hasData ? 'Yes' : 'No'}
                </span></div>
              )}
              
              {testResult.dataKeys && testResult.dataKeys.length > 0 && (
                <div>Data Keys: {testResult.dataKeys.join(', ')}</div>
              )}
              
              {testResult.error && (
                <div>Error: <span className="text-red-600">{JSON.stringify(testResult.error)}</span></div>
              )}
              
              <details className="mt-2">
                <summary className="cursor-pointer font-medium">Full Response</summary>
                <pre className="mt-2 text-xs bg-white p-2 rounded overflow-auto max-h-40">
                  {JSON.stringify(testResult.fullResponse, null, 2)}
                </pre>
              </details>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
